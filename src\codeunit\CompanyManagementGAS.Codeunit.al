codeunit 60004 "Company Management GAS"
{
    var
        ConfirmManagement: Codeunit "Confirm Management";
        AtLeastOneCompanyErr: Label 'Cannot delete all companies. At least one company must remain in the system.';
        CurrentCompanyProtectedErr: Label 'Cannot delete the current company. Please switch to a different company before performing this operation.';
        CompanyDeletionFailedErr: Label 'Failed to delete company %1. Error: %2', Comment = '%1 = Company name, %2 = Error message';
        InsufficientPermissionsErr: Label 'You do not have sufficient permissions to delete companies. Contact your system administrator.';
        CriticalCompanyErr: Label 'Cannot delete critical companies: %1. These companies contain important system data.', Comment = '%1 = List of critical company names';

    /// <summary>
    /// Deletes all companies that are not marked for keeping, with comprehensive safety validations.
    /// </summary>
    procedure DeleteUnmarkedCompanies()
    var
        CurrentCompanyName: Text[30];
        CompaniesToDelete: List of [Text[30]];
        CompanyName: Text[30];
        DeletedCount: Integer;
        SkippedCount: Integer;
        Window: Dialog;
        Counter: Integer;
        TotalCount: Integer;
        ConfirmDeleteQst: Label 'This will delete all companies that are not marked as "Keep Company". This action cannot be undone.\\ %1 companies will be deleted.\\ Do you want to continue?', Comment = '%1 = Number of companies to delete';
        DeletionInProgressMsg: Label 'Deleting companies...\Company #1####### of #2#######\Current: #3################################', Comment = '#1 = Current company counter, #2 = Total companies, #3 = Current company name';
        DeletionCompletedMsg: Label '%1 companies deleted successfully. %2 companies were skipped.', Comment = '%1 = Deleted count, %2 = Skipped count';
        NoCompaniesToDeleteMsg: Label 'No companies found for deletion. All companies are either marked to keep or are system companies.';
    begin
        // Validate user permissions
        ValidateUserPermissions();

        // Get current company name
        CurrentCompanyName := CopyStr(CompanyName(), 1, MaxStrLen(CurrentCompanyName));

        // Validate that we're not trying to delete the current company
        ValidateCurrentCompanyProtection(CurrentCompanyName);

        // Build list of companies to delete
        BuildCompanyDeletionList(CompaniesToDelete, CurrentCompanyName);

        // Validate that at least one company will remain
        ValidateAtLeastOneCompanyRemains(CompaniesToDelete);

        // Additional safety validations
        ValidateCriticalCompanies(CompaniesToDelete);

        // Show confirmation dialog
        if CompaniesToDelete.Count() = 0 then begin
            Message(NoCompaniesToDeleteMsg);
            exit;
        end;

        if not ConfirmManagement.GetResponseOrDefault(StrSubstNo(ConfirmDeleteQst, CompaniesToDelete.Count()), false) then
            exit;

        // Log the operation start
        LogCompanyDeletionOperation('START', CompaniesToDelete);

        // Perform deletion with progress dialog
        TotalCount := CompaniesToDelete.Count();
        Window.Open(DeletionInProgressMsg);

        foreach CompanyName in CompaniesToDelete do begin
            Counter += 1;
            Window.Update(1, Counter);
            Window.Update(2, TotalCount);
            Window.Update(3, CompanyName);

            if DeleteSingleCompany(CompanyName) then
                DeletedCount += 1
            else
                SkippedCount += 1;
        end;

        Window.Close();

        // Log the operation completion
        LogCompanyDeletionOperation('COMPLETE', CompaniesToDelete, DeletedCount, SkippedCount);

        Message(DeletionCompletedMsg, DeletedCount, SkippedCount);
    end;

    local procedure BuildCompanyDeletionList(var CompaniesToDelete: List of [Text[30]]; CurrentCompanyName: Text[30])
    var
        Company: Record Company;
        CompanyInformation: Record "Company Information";
    begin
        Clear(CompaniesToDelete);

        Company.SetLoadFields(Name);
        if Company.FindSet() then
            repeat
                // Skip current company
                if Company.Name <> CurrentCompanyName then begin
                    // Check if company is marked to keep
                    if not IsCompanyMarkedToKeep(Company.Name) then
                        // Skip system companies (those without Company Information records)
                        if not IsSystemCompany(Company.Name) then
                            CompaniesToDelete.Add(Company.Name);
                end;
            until Company.Next() = 0;
    end;

    local procedure IsCompanyMarkedToKeep(CompanyName: Text[30]): Boolean
    var
        CompanyProtection: Record "Company Protection GAS";
    begin
        if CompanyProtection.Get(CompanyName) then
            exit(CompanyProtection."Keep Company");

        exit(false);
    end;

    local procedure IsSystemCompany(CompanyName: Text[30]): Boolean
    var
        CompanyInformation: Record "Company Information";
    begin
        // Consider companies without Company Information records as system companies
        CompanyInformation.ChangeCompany(CompanyName);
        exit(not CompanyInformation.Get());
    end;

    local procedure ValidateCurrentCompanyProtection(CurrentCompanyName: Text[30])
    var
        CompanyProtection: Record "Company Protection GAS";
    begin
        // Always protect the current company from deletion
        if CompanyProtection.Get(CurrentCompanyName) then
            if not CompanyProtection."Keep Company" then
                Error(CurrentCompanyProtectedErr);
    end;

    local procedure ValidateAtLeastOneCompanyRemains(var CompaniesToDelete: List of [Text[30]])
    var
        Company: Record Company;
        TotalCompanies: Integer;
    begin
        Company.SetLoadFields(Name);
        TotalCompanies := Company.Count();

        if CompaniesToDelete.Count() >= TotalCompanies then
            Error(AtLeastOneCompanyErr);
    end;

    local procedure DeleteSingleCompany(CompanyName: Text[30]): Boolean
    var
        Company: Record Company;
    begin
        Company.SetLoadFields(Name);
        Company.SetRange(Name, CompanyName);
        if Company.FindFirst() then begin
            if Company.Delete(true) then
                exit(true)
            else begin
                Message(CompanyDeletionFailedErr, CompanyName, GetLastErrorText());
                ClearLastError();
                exit(false);
            end;
        end;

        exit(false);
    end;

    /// <summary>
    /// Marks or unmarks a company for protection from bulk deletion.
    /// </summary>
    /// <param name="CompanyName">The name of the company to mark/unmark.</param>
    /// <param name="KeepCompany">True to protect the company, false to remove protection.</param>
    procedure MarkCompanyForKeeping(CompanyName: Text[30]; KeepCompany: Boolean)
    var
        CompanyProtection: Record "Company Protection GAS";
    begin
        if CompanyProtection.Get(CompanyName) then begin
            CompanyProtection."Keep Company" := KeepCompany;
            CompanyProtection.Modify(true);
        end else begin
            CompanyProtection.Init();
            CompanyProtection."Company Name" := CompanyName;
            CompanyProtection."Keep Company" := KeepCompany;
            CompanyProtection.Insert(true);
        end;
    end;

    procedure GetCompanyKeepStatus(CompanyName: Text[30]): Boolean
    var
        CompanyProtection: Record "Company Protection GAS";
    begin
        if CompanyProtection.Get(CompanyName) then
            exit(CompanyProtection."Keep Company");

        exit(false);
    end;

    /// <summary>
    /// Gets the protection status of a company.
    /// </summary>
    /// <param name="CompanyName">The name of the company to check.</param>
    /// <returns>True if the company is marked for keeping, false otherwise.</returns>
    procedure GetCompanyKeepStatus(CompanyName: Text[30]): Boolean
    begin
        exit(IsCompanyMarkedToKeep(CompanyName));
    end;

    local procedure ValidateUserPermissions()
    var
        Company: Record Company;
    begin
        // Test if user can access Company table
        if not Company.ReadPermission() then
            Error(InsufficientPermissionsErr);

        // Test if user can modify Company table (delete requires modify permission)
        if not Company.WritePermission() then
            Error(InsufficientPermissionsErr);
    end;

    local procedure ValidateCriticalCompanies(var CompaniesToDelete: List of [Text[30]])
    var
        CompanyName: Text[30];
        CriticalCompanyNames: List of [Text[30]];
        CriticalCompaniesFound: Text;
    begin
        // Define critical company names that should never be deleted
        CriticalCompanyNames.Add('CRONUS');
        CriticalCompanyNames.Add('EVALUATION');
        CriticalCompanyNames.Add('DEMO');

        foreach CompanyName in CompaniesToDelete do begin
            if IsCriticalCompany(CompanyName, CriticalCompanyNames) then begin
                if CriticalCompaniesFound <> '' then
                    CriticalCompaniesFound += ', ';
                CriticalCompaniesFound += CompanyName;
            end;
        end;

        if CriticalCompaniesFound <> '' then
            Error(CriticalCompanyErr, CriticalCompaniesFound);
    end;

    local procedure IsCriticalCompany(CompanyName: Text[30]; CriticalCompanyNames: List of [Text[30]]): Boolean
    var
        CriticalName: Text[30];
    begin
        foreach CriticalName in CriticalCompanyNames do begin
            if StrPos(UpperCase(CompanyName), UpperCase(CriticalName)) > 0 then
                exit(true);
        end;
        exit(false);
    end;

    local procedure LogCompanyDeletionOperation(OperationType: Text[10]; CompaniesToDelete: List of [Text[30]]; DeletedCount: Integer; SkippedCount: Integer)
    var
        CompanyName: Text[30];
        CompanyList: Text;
        LogMessage: Text;
        StartLogMsg: Label 'Company deletion operation started. Companies to delete: %1', Comment = '%1 = List of company names';
        CompleteLogMsg: Label 'Company deletion operation completed. Deleted: %1, Skipped: %2, Companies: %3', Comment = '%1 = Deleted count, %2 = Skipped count, %3 = Company list';
    begin
        // Build company list for logging
        foreach CompanyName in CompaniesToDelete do begin
            if CompanyList <> '' then
                CompanyList += ', ';
            CompanyList += CompanyName;
        end;

        // Create log message based on operation type
        case OperationType of
            'START':
                LogMessage := StrSubstNo(StartLogMsg, CompanyList);
            'COMPLETE':
                LogMessage := StrSubstNo(CompleteLogMsg, DeletedCount, SkippedCount, CompanyList);
        end;

        // Log to system using Session.LogMessage for audit trail
        Session.LogMessage('0001', LogMessage, Verbosity::Normal, DataClassification::SystemMetadata, TelemetryScope::ExtensionPublisher, 'Category', 'CompanyDeletion');
    end;

    local procedure LogCompanyDeletionOperation(OperationType: Text[10]; CompaniesToDelete: List of [Text[30]])
    begin
        LogCompanyDeletionOperation(OperationType, CompaniesToDelete, 0, 0);
    end;
}
