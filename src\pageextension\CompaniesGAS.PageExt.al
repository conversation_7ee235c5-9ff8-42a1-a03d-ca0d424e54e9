pageextension 60016 "Companies GAS" extends 357
{
    layout
    {
        addafter("Display Name")
        {
            field("Keep Company GAS"; KeepCompanyStatus)
            {
                ApplicationArea = All;
                Caption = 'Keep Company';
                ToolTip = 'Specifies whether this company is marked for protection from bulk deletion.';

                trigger OnValidate()
                var
                    CompanyManagement: Codeunit 60004;
                begin
                    CompanyManagement.MarkCompanyForKeeping(Rec.Name, KeepCompanyStatus);
                    CurrPage.Update(false);
                end;
            }
            field("Current Company GAS"; IsCurrentCompany)
            {
                ApplicationArea = All;
                Caption = 'Current Company';
                ToolTip = 'Specifies if this is the currently active company.';
                Editable = false;
                StyleExpr = CurrentCompanyStyle;
            }
            field("System Company GAS"; IsSystemCompany)
            {
                ApplicationArea = All;
                Caption = 'System Company';
                ToolTip = 'Specifies if this is a system company without Company Information.';
                Editable = false;
                StyleExpr = SystemCompanyStyle;
            }
        }
    }

    actions
    {
        addfirst(processing)
        {
            group("Company Management GAS")
            {
                Caption = 'Company Management';
                Image = Administration;

                action("Delete Unmarked Companies GAS")
                {
                    ApplicationArea = All;
                    Caption = 'Delete Unmarked Companies';
                    Image = Delete;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'Delete all companies that are not marked for keeping. This action cannot be undone.';

                    trigger OnAction()
                    var
                        CompanyManagement: codeunit "Company Management GAS";
                    begin
                        CompanyManagement.DeleteUnmarkedCompanies();
                        CurrPage.Update(false);
                    end;
                }

                action("Mark All for Keeping GAS")
                {
                    ApplicationArea = All;
                    Caption = 'Mark All for Keeping';
                    Image = Approve;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    ToolTip = 'Mark all companies for protection from deletion.';

                    trigger OnAction()
                    begin
                        MarkAllCompanies(true);
                    end;
                }

                action("Unmark All for Keeping GAS")
                {
                    ApplicationArea = All;
                    Caption = 'Unmark All for Keeping';
                    Image = RemoveFilterLines;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    ToolTip = 'Remove protection marking from all companies (except current company).';

                    trigger OnAction()
                    begin
                        MarkAllCompanies(false);
                    end;
                }

                action("Refresh GAS")
                {
                    ApplicationArea = All;
                    Caption = 'Refresh';
                    Image = Refresh;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedOnly = true;
                    ToolTip = 'Refresh the company list and protection status.';

                    trigger OnAction()
                    begin
                        CurrPage.Update(false);
                    end;
                }

                action("Company Management List GAS")
                {
                    ApplicationArea = All;
                    Caption = 'Company Management List';
                    Image = List;
                    Promoted = true;
                    PromotedCategory = Process;
                    ToolTip = 'Open the detailed company management list to view and manage all companies.';

                    trigger OnAction()
                    begin
                        page.Run(page::"Company Management List GAS");
                    end;
                }
            }
        }

        area(Navigation)
        {
            action("Company Information GAS")
            {
                ApplicationArea = All;
                Caption = 'Company Information';
                Image = Info;
                ToolTip = 'Open the Company Information page for the selected company.';

                trigger OnAction()
                var
                    CompanyInformation: Record "Company Information";
                begin
                    CompanyInformation.ChangeCompany(Rec.Name);
                    if CompanyInformation.Get() then begin
                        CompanyInformation.SetRecFilter();
                        page.Run(page::"Company Information", CompanyInformation);
                    end;
                end;
            }
        }
    }

    var
        KeepCompanyStatus: Boolean;
        IsCurrentCompany: Boolean;
        IsSystemCompany: Boolean;
        CurrentCompanyStyle: Text;
        SystemCompanyStyle: Text;

    trigger OnAfterGetRecord()
    var
        CompanyManagement: codeunit 60004;
        CurrentCompanyName: Text[30];
    begin
        // Get company protection status from Company Protection table
        KeepCompanyStatus := CompanyManagement.GetCompanyKeepStatus(Rec.Name);

        // Check if this is the current company
        CurrentCompanyName := CopyStr(CompanyName(), 1, MaxStrLen(CurrentCompanyName));
        IsCurrentCompany := (Rec.Name = CurrentCompanyName);

        // Check if this is a system company
        IsSystemCompany := IsSystemCompanyFunc(Rec.Name);

        // Set styles
        if IsCurrentCompany then
            CurrentCompanyStyle := 'Strong'
        else
            CurrentCompanyStyle := '';

        if IsSystemCompany then
            SystemCompanyStyle := 'Subordinate'
        else
            SystemCompanyStyle := '';
    end;

    local procedure IsSystemCompanyFunc(CompanyName: Text[30]): Boolean
    var
        CompanyInformation: Record "Company Information";
    begin
        // Consider companies without Company Information records as system companies
        CompanyInformation.ChangeCompany(CompanyName);
        exit(not CompanyInformation.Get());
    end;

    local procedure MarkAllCompanies(KeepStatus: Boolean)
    var
        Company: Record Company;
        CompanyManagement: Codeunit 60004;
        CurrentCompanyName: Text[30];
        Counter: Integer;
        TotalCount: Integer;
        Window: Dialog;
        ProcessingMsg: Label 'Processing companies...\Company #1####### of #2#######', Comment = '#1 = Current company counter, #2 = Total companies';
    begin
        CurrentCompanyName := CopyStr(CompanyName(), 1, MaxStrLen(CurrentCompanyName));

        Company.SetLoadFields(Name);
        TotalCount := Company.Count();
        Window.Open(ProcessingMsg);

        if Company.FindSet() then
            repeat
                Counter += 1;
                Window.Update(1, Counter);
                Window.Update(2, TotalCount);

                // Skip current company when unmarking (always keep current company protected)
                if not ((Company.Name = CurrentCompanyName) and (not KeepStatus)) then
                    CompanyManagement.MarkCompanyForKeeping(Company.Name, KeepStatus);
            until Company.Next() = 0;

        Window.Close();
        CurrPage.Update(false);
    end;
}
