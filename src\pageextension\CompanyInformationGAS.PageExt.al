pageextension 60015 "Company Information GAS" extends "Company Information"
{
    layout
    {
        addafter(General)
        {
            group("Company Protection GAS")
            {
                Caption = 'Company Protection';

                field("Keep Company GAS"; Rec."Keep Company GAS")
                {
                    ApplicationArea = All;
                }
            }
        }
    }

    actions
    {
        addfirst(Processing)
        {
            group("Company Management GAS")
            {
                Caption = 'Company Management';
                Image = Administration;

                action("Delete Unmarked Companies GAS")
                {
                    ApplicationArea = All;
                    Caption = 'Delete Unmarked Companies';
                    Image = Delete;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    ToolTip = 'Delete all companies that are not marked for keeping. This action cannot be undone.';

                    trigger OnAction()
                    var
                        CompanyManagement: Codeunit "Company Management GAS";
                    begin
                        CompanyManagement.DeleteUnmarkedCompanies();
                    end;
                }

                action("Company Management List GAS")
                {
                    ApplicationArea = All;
                    Caption = 'Company Management List';
                    Image = List;
                    Promoted = true;
                    PromotedCategory = Process;
                    ToolTip = 'Open the company management list to view and manage all companies.';

                    trigger OnAction()
                    begin
                        Page.Run(Page::"Company Management List GAS");
                    end;
                }
            }
        }
    }
}
