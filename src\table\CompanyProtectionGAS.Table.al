table 60012 "Company Protection GAS"
{
    Caption = 'Company Protection';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Company Name"; Text[30])
        {
            Caption = 'Company Name';
            DataClassification = CustomerContent;
            TableRelation = Company.Name;
        }
        field(2; "Keep Company"; Boolean)
        {
            Caption = 'Keep Company';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies whether this company should be protected from bulk deletion operations.';
        }
    }

    keys
    {
        key(PK; "Company Name")
        {
            Clustered = true;
        }
    }
}
