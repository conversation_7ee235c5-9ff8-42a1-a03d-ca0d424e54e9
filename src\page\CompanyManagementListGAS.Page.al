page 60015 "Company Management List GAS"
{
    PageType = List;
    ApplicationArea = All;
    Extensible = false;
    UsageCategory = Administration;
    SourceTable = Company;
    Caption = 'Company Management';
    Editable = false;
    InsertAllowed = false;
    DeleteAllowed = false;
    ModifyAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(Companies)
            {
                field(Name; Rec.Name)
                {
                    ToolTip = 'Specifies the name of the company.';
                }
                field("Display Name"; Rec."Display Name")
                {
                    ToolTip = 'Specifies the display name of the company.';
                }
                field("Keep Company GAS"; this.KeepCompanyStatus)
                {
                    Caption = 'Keep Company';
                    ToolTip = 'Specifies whether this company is marked for protection from bulk deletion.';

                    trigger OnValidate()
                    var
                        CompanyManagement: Codeunit "Company Management GAS";
                    begin
                        CompanyManagement.MarkCompanyForKeeping(Rec.Name, this.KeepCompanyStatus);
                        CurrPage.Update(false);
                    end;
                }
                field("Current Company"; this.IsCurrentCompany)
                {
                    Caption = 'Current Company';
                    ToolTip = 'Specifies if this is the currently active company.';
                    Editable = false;
                    StyleExpr = this.CurrentCompanyStyle;
                }
                field("System Company"; this.IsSystemCompany)
                {
                    Caption = 'System Company';
                    ToolTip = 'Specifies if this is a system company without Company Information.';
                    Editable = false;
                    StyleExpr = this.SystemCompanyStyle;
                }
            }
        }
        area(FactBoxes)
        {
            systempart(Links; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(Notes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action("Delete Unmarked Companies GAS")
            {
                ApplicationArea = All;
                Caption = 'Delete Unmarked Companies';
                Image = Delete;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Delete all companies that are not marked for keeping. This action cannot be undone.';

                trigger OnAction()
                var
                    CompanyManagement: Codeunit "Company Management GAS";
                begin
                    CompanyManagement.DeleteUnmarkedCompanies();
                    CurrPage.Update(false);
                end;
            }

            action("Mark All for Keeping GAS")
            {
                ApplicationArea = All;
                Caption = 'Mark All for Keeping';
                Image = Approve;
                Promoted = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                ToolTip = 'Mark all companies for protection from deletion.';

                trigger OnAction()
                begin
                    this.MarkAllCompanies(true);
                end;
            }

            action("Unmark All for Keeping GAS")
            {
                ApplicationArea = All;
                Caption = 'Unmark All for Keeping';
                Image = RemoveFilterLines;
                Promoted = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                ToolTip = 'Remove protection marking from all companies (except current company).';

                trigger OnAction()
                begin
                    this.MarkAllCompanies(false);
                end;
            }

            action("Refresh GAS")
            {
                ApplicationArea = All;
                Caption = 'Refresh';
                Image = Refresh;
                Promoted = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                ToolTip = 'Refresh the company list and protection status.';

                trigger OnAction()
                begin
                    CurrPage.Update(false);
                end;
            }
        }
        area(Navigation)
        {
            action("Company Information GAS")
            {
                ApplicationArea = All;
                Caption = 'Company Information';
                Image = Info;
                ToolTip = 'Open the Company Information page for the selected company.';

                trigger OnAction()
                var
                    CompanyInformation: Record "Company Information";
                begin
                    CompanyInformation.ChangeCompany(Rec.Name);
                    if CompanyInformation.Get() then begin
                        CompanyInformation.SetRecFilter();
                        Page.Run(Page::"Company Information", CompanyInformation);
                    end;
                end;
            }
        }
    }

    var
        KeepCompanyStatus: Boolean;
        IsCurrentCompany: Boolean;
        IsSystemCompany: Boolean;
        CurrentCompanyStyle: Text;
        SystemCompanyStyle: Text;

    trigger OnAfterGetRecord()
    var
        CompanyManagement: Codeunit "Company Management GAS";
        CurrentCompanyName: Text[30];
    begin
        // Get company protection status
        this.KeepCompanyStatus := CompanyManagement.GetCompanyKeepStatus(Rec.Name);

        // Check if this is the current company
        CurrentCompanyName := CopyStr(CompanyName(), 1, MaxStrLen(CurrentCompanyName));
        this.IsCurrentCompany := (Rec.Name = CurrentCompanyName);

        // Check if this is a system company
        this.IsSystemCompany := this.IsSystemCompanyFunc(Rec.Name);

        // Set styles
        if this.IsCurrentCompany then
            this.CurrentCompanyStyle := Format(PageStyle::Strong)
        else
            this.CurrentCompanyStyle := '';

        if this.IsSystemCompany then
            this.SystemCompanyStyle := Format(PageStyle::Attention)
        else
            this.SystemCompanyStyle := '';
    end;

    local procedure IsSystemCompanyFunc(CompanyName: Text[30]): Boolean
    var
        CompanyInformation: Record "Company Information";
    begin
        CompanyInformation.ChangeCompany(CompanyName);
        exit(not CompanyInformation.Get());
    end;

    local procedure MarkAllCompanies(KeepStatus: Boolean)
    var
        Company: Record Company;
        CompanyManagement: Codeunit "Company Management GAS";
        CurrentCompanyName: Text[30];
        Counter: Integer;
        Window: Dialog;
        ProcessingMsg: Label 'Processing companies...\Company #1####### of #2#######', Comment = '#1 = Current company counter, #2 = Total companies';
    begin
        CurrentCompanyName := CopyStr(CompanyName(), 1, MaxStrLen(CurrentCompanyName));

        Company.SetLoadFields(Name);
        Window.Open(ProcessingMsg);

        if Company.FindSet() then
            repeat
                Counter += 1;
                Window.Update(1, Counter);
                Window.Update(2, Company.Count());

                // Always keep the current company marked for protection
                if Company.Name = CurrentCompanyName then
                    CompanyManagement.MarkCompanyForKeeping(Company.Name, true)
                else
                    CompanyManagement.MarkCompanyForKeeping(Company.Name, KeepStatus);
            until Company.Next() = 0;

        Window.Close();
        CurrPage.Update(false);
    end;
}
